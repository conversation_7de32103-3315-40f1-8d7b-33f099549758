/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/rover/page";
exports.ids = ["app/rover/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frover%2Fpage&page=%2Frover%2Fpage&appPaths=%2Frover%2Fpage&pagePath=private-next-app-dir%2Frover%2Fpage.tsx&appDir=D%3A%5CWebRover-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebRover-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frover%2Fpage&page=%2Frover%2Fpage&appPaths=%2Frover%2Fpage&pagePath=private-next-app-dir%2Frover%2Fpage.tsx&appDir=D%3A%5CWebRover-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebRover-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/rover/page.tsx */ \"(rsc)/./src/app/rover/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'rover',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/rover/page\",\n        pathname: \"/rover\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZyb3ZlciUyRnBhZ2UmcGFnZT0lMkZyb3ZlciUyRnBhZ2UmYXBwUGF0aHM9JTJGcm92ZXIlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcm92ZXIlMkZwYWdlLnRzeCZhcHBEaXI9RCUzQSU1Q1dlYlJvdmVyLW1haW4lNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q1dlYlJvdmVyLW1haW4lNUNmcm9udGVuZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0JBQXNCLG9KQUFzRjtBQUM1RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLGdPQUFtRjtBQUN6RyxvQkFBb0IsNEpBQTJGO0FBRzdHO0FBQ3NEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQThPO0FBQ2xSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBOE87QUFDbFI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJSb3Zlci1tYWluXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IHBhZ2U0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJSb3Zlci1tYWluXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccm92ZXJcXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAncm92ZXInLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTQsIFwiRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHJvdmVyXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUQ6XFxcXFdlYlJvdmVyLW1haW5cXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkQ6XFxcXFdlYlJvdmVyLW1haW5cXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUxLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTIsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHJvdmVyXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcm92ZXIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvcm92ZXJcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frover%2Fpage&page=%2Frover%2Fpage&appPaths=%2Frover%2Fpage&pagePath=private-next-app-dir%2Frover%2Fpage.tsx&appDir=D%3A%5CWebRover-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebRover-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDV2ViUm92ZXItbWFpbiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1dlYlJvdmVyLW1haW4lNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2xpYiU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUE4SDtBQUM5SDtBQUNBLDBPQUFpSTtBQUNqSTtBQUNBLDBPQUFpSTtBQUNqSTtBQUNBLG9SQUF1SjtBQUN2SjtBQUNBLHdPQUFnSTtBQUNoSTtBQUNBLHNRQUErSTtBQUMvSTtBQUNBLHNPQUErSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJSb3Zlci1tYWluXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFdlYlJvdmVyLW1haW5cXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJSb3Zlci1tYWluXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFdlYlJvdmVyLW1haW5cXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxsaWJcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ParticlesBackground.tsx */ \"(rsc)/./src/components/ui/ParticlesBackground.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ParticlesBackground.tsx */ \"(ssr)/./src/components/ui/ParticlesBackground.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Crover%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Crover%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/rover/page.tsx */ \"(rsc)/./src/app/rover/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNyb3ZlciU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFdlYlJvdmVyLW1haW5cXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxyb3ZlclxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Crover%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Crover%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Crover%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/rover/page.tsx */ \"(ssr)/./src/app/rover/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNyb3ZlciU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFdlYlJvdmVyLW1haW5cXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxyb3ZlclxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Crover%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/rover/page.tsx":
/*!********************************!*\
  !*** ./src/app/rover/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoverPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_rover_ResponseDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/rover/ResponseDisplay */ \"(ssr)/./src/components/rover/ResponseDisplay.tsx\");\n/* harmony import */ var _components_rover_QueryInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/rover/QueryInput */ \"(ssr)/./src/components/rover/QueryInput.tsx\");\n/* harmony import */ var _components_ui_ParticlesBackground__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ParticlesBackground */ \"(ssr)/./src/components/ui/ParticlesBackground.tsx\");\n/* harmony import */ var _components_ui_ToggleSwitch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ToggleSwitch */ \"(ssr)/./src/components/ui/ToggleSwitch.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction RoverPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResearchMode, setIsResearchMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeepResearch, setIsDeepResearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const currentAgent = isResearchMode ? isDeepResearch ? 'deep_research' : 'research' : 'task';\n    const handleDisconnect = async ()=>{\n        try {\n            const response = await fetch('http://localhost:8000/cleanup', {\n                method: 'POST'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to cleanup browser');\n            }\n            await router.push('/');\n        } catch (error) {\n            console.error('Failed to cleanup browser:', error);\n            // Still try to navigate even if cleanup fails\n            await router.push('/');\n        }\n    };\n    const handleStreamingResponse = async (response)=>{\n        const reader = response.body?.getReader();\n        if (!reader) throw new Error('No response reader');\n        const decoder = new TextDecoder();\n        let buffer = '';\n        const processSSEMessage = (message)=>{\n            try {\n                const jsonStr = message.replace(/^data: /, '').trim();\n                const data = JSON.parse(jsonStr);\n                if (data.type === 'keepalive') return;\n                // Clean content if it matches the pattern\n                const cleanContent = (content)=>{\n                    // If content is already an array, return it directly\n                    if (Array.isArray(content)) {\n                        return content;\n                    }\n                    if (typeof content === 'string') {\n                        try {\n                            // Try to parse as JSON\n                            const parsed = JSON.parse(content);\n                            if (Array.isArray(parsed)) {\n                                return parsed;\n                            }\n                            // If it's a string with [\"...\"] pattern\n                            if (content.startsWith('[\"') && content.endsWith('\"]')) {\n                                return content.slice(2, -2);\n                            }\n                        } catch  {\n                            // If parsing fails and it has the pattern\n                            if (content.startsWith('[\"') && content.endsWith('\"]')) {\n                                return content.slice(2, -2);\n                            }\n                        }\n                    }\n                    return content;\n                };\n                const processedData = {\n                    type: data.type,\n                    content: cleanContent(data.content)\n                };\n                // Handle different message types based on agent\n                switch(data.type){\n                    case 'thought':\n                    case 'action':\n                    case 'browser_action':\n                    case 'final_answer':\n                    case 'final_response':\n                    case 'dom_update':\n                    case 'interaction':\n                        setMessages((prev)=>[\n                                ...prev,\n                                processedData\n                            ]);\n                        break;\n                    // Research specific events\n                    case 'rag_action':\n                    case 'review':\n                    case 'close_tab':\n                    case 'cleanup':\n                        if (isResearchMode) {\n                            setMessages((prev)=>[\n                                    ...prev,\n                                    processedData\n                                ]);\n                        }\n                        break;\n                    // Deep research specific events\n                    case 'subtopics':\n                    case 'subtopic_answer':\n                    case 'subtopic_status':\n                    case 'compile':\n                        if (isResearchMode && isDeepResearch) {\n                            setMessages((prev)=>[\n                                    ...prev,\n                                    processedData\n                                ]);\n                        }\n                        break;\n                    case 'error':\n                        setMessages((prev)=>[\n                                ...prev,\n                                processedData\n                            ]);\n                        break;\n                }\n            } catch (e) {\n                console.error('Failed to parse SSE message:', message, e);\n            }\n        };\n        while(true){\n            const { done, value } = await reader.read();\n            if (done) break;\n            buffer += decoder.decode(value, {\n                stream: true\n            });\n            // Find complete SSE messages\n            const messages = buffer.match(/data: {[\\s\\S]*?}\\n\\n/g);\n            if (messages) {\n                messages.forEach(processSSEMessage);\n                // Remove processed messages from buffer\n                buffer = buffer.slice(buffer.lastIndexOf('}') + 1);\n            }\n        }\n    };\n    const handleSubmit = async (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        if (!query.trim() || isLoading) return;\n        setIsLoading(true);\n        // Add user message to the chat history\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    type: 'user_input',\n                    content: query\n                }\n            ]);\n        const currentQuery = query;\n        setQuery(''); // Clear input after sending\n        try {\n            const response = await fetch('http://localhost:8000/query', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: currentQuery,\n                    agent_type: currentAgent\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || 'Failed to send query');\n            }\n            await handleStreamingResponse(response);\n        } catch (error) {\n            console.error('Query failed:', error);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        type: 'error',\n                        content: error?.message || 'Failed to process query. Please try again.'\n                    }\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative min-h-screen bg-gradient-to-b from-slate-950 via-indigo-950/20 to-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParticlesBackground__WEBPACK_IMPORTED_MODULE_5__.ParticlesBackground, {}, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 p-4 backdrop-blur-xl bg-black/30 z-50 border-b border-zinc-800/50 shadow-lg shadow-black/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center max-w-[1600px] mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400  text-transparent bg-clip-text animate-flow bg-[length:200%_auto]\",\n                            children: \"SmartSurf\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex justify-center items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center min-w-[280px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Task\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ToggleSwitch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                        enabled: isResearchMode,\n                                                        onChange: setIsResearchMode,\n                                                        label: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Research\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-zinc-500 mt-2\",\n                                                children: \"Switch between Task and Research agents\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex flex-col items-center min-w-[280px] transition-opacity duration-300 ${isResearchMode ? 'opacity-100' : 'opacity-0'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Normal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ToggleSwitch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                        enabled: isDeepResearch,\n                                                        onChange: setIsDeepResearch,\n                                                        label: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Deep Research\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-zinc-500 mt-2\",\n                                                children: \"Enable comprehensive research mode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleDisconnect,\n                            className: \"px-4 py-2 rounded-full whitespace-nowrap bg-gradient-to-r from-rose-500/10 to-pink-500/10 border border-rose-500/50 text-rose-400 hover:bg-rose-500/20 hover:border-rose-500/70 hover:text-rose-300 transition-all duration-300\",\n                            children: \"Disconnect Browser\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-8 left-1/2 transform -translate-x-1/2 z-40 w-full max-w-[800px] px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_rover_QueryInput__WEBPACK_IMPORTED_MODULE_4__.QueryInput, {\n                    value: query,\n                    onChange: setQuery,\n                    onSubmit: handleSubmit,\n                    isLoading: isLoading\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative pt-24 pb-32 z-10 overflow-y-auto h-[calc(100vh-140px)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full pb-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_rover_ResponseDisplay__WEBPACK_IMPORTED_MODULE_3__.ResponseDisplay, {\n                        messages: messages\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/rover/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/rover/QueryInput.tsx":
/*!*********************************************!*\
  !*** ./src/components/rover/QueryInput.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryInput: () => (/* binding */ QueryInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction QueryInput({ value, onChange, onSubmit, isLoading }) {\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-resize textarea height\n    const adjustHeight = ()=>{\n        if (inputRef.current) {\n            inputRef.current.style.height = 'auto';\n            inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;\n        }\n    };\n    // Handle input change\n    const handleChange = (e)=>{\n        onChange(e.target.value);\n        adjustHeight();\n    };\n    // Handle key press\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            onSubmit();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black via-black/95 to-transparent\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto pb-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        ref: inputRef,\n                        value: value,\n                        onChange: handleChange,\n                        onKeyDown: handleKeyPress,\n                        placeholder: \"Ask SmartSurf anything...\",\n                        rows: 1,\n                        className: \"w-full px-6 py-4 pr-24 bg-zinc-900/50 backdrop-blur-sm  border border-zinc-800 rounded-2xl text-zinc-100 placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 hover:border-zinc-700 transition-all duration-300 resize-none overflow-hidden min-h-[60px] max-h-[120px]\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\QueryInput.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onSubmit,\n                        disabled: isLoading || !value.trim(),\n                        className: \"absolute right-2 top-2 px-6 py-2.5  bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl text-white font-medium hover:opacity-90 hover:shadow-lg hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-300\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-5 h-5 border-2 border-white/30 border-t-white/90  rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\QueryInput.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 15\n                        }, this) : 'Send'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\QueryInput.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\QueryInput.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\QueryInput.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\QueryInput.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/rover/QueryInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/rover/ResponseActions.tsx":
/*!**************************************************!*\
  !*** ./src/components/rover/ResponseActions.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponseActions: () => (/* binding */ ResponseActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CheckIcon_DocumentIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CheckIcon,DocumentIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CheckIcon_DocumentIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CheckIcon,DocumentIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CheckIcon_DocumentIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CheckIcon,DocumentIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jspdf */ \"(ssr)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n\n\n\n\nfunction ResponseActions({ content, isResearchResponse }) {\n    const [copying, setCopying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [typing, setTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = async ()=>{\n        try {\n            setCopying(true);\n            await navigator.clipboard.writeText(content);\n            setTimeout(()=>setCopying(false), 1000);\n        } catch (error) {\n            console.error('Failed to copy:', error);\n            setCopying(false);\n        }\n    };\n    const handleTypeInDocs = async ()=>{\n        try {\n            setTyping(true);\n            const response = await fetch('http://localhost:8000/api/docs/type', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to type in docs');\n            }\n            const data = await response.json();\n            console.log('Type in docs response:', data);\n        } catch (error) {\n            console.error('Failed to type in docs:', error);\n        } finally{\n            setTyping(false);\n        }\n    };\n    const handleDownloadPDF = async ()=>{\n        try {\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_2__.jsPDF({\n                unit: 'pt',\n                format: 'a4',\n                hotfixes: [\n                    'px_scaling'\n                ]\n            });\n            const pageWidth = pdf.internal.pageSize.width;\n            const pageHeight = pdf.internal.pageSize.height;\n            let y = 50;\n            const margin = 50;\n            const maxWidth = pageWidth - margin * 2;\n            const lineHeight = 1.5;\n            const splitTextToSize = (text, fontSize)=>{\n                pdf.setFontSize(fontSize);\n                return pdf.splitTextToSize(text, maxWidth);\n            };\n            const processBoldText = (text)=>{\n                const parts = text.split(/(\\*\\*.*?\\*\\*)/g);\n                let currentX = margin;\n                parts.forEach((part)=>{\n                    if (part.startsWith('**') && part.endsWith('**')) {\n                        const boldText = part.replace(/\\*\\*/g, '');\n                        pdf.setFont('helvetica', 'bold');\n                        const wrappedBoldText = splitTextToSize(boldText, pdf.getFontSize());\n                        wrappedBoldText.forEach((line, index)=>{\n                            pdf.text(line, currentX, y + index * pdf.getFontSize() * lineHeight);\n                        });\n                        currentX += pdf.getTextWidth(boldText) + 2;\n                    } else if (part.trim().length > 0) {\n                        pdf.setFont('helvetica', 'normal');\n                        const wrappedText = splitTextToSize(part, pdf.getFontSize());\n                        wrappedText.forEach((line, index)=>{\n                            pdf.text(line, currentX, y + index * pdf.getFontSize() * lineHeight);\n                        });\n                        currentX += pdf.getTextWidth(part) + 2;\n                    }\n                });\n            };\n            const checkNewPage = (requiredHeight)=>{\n                if (y + requiredHeight > pageHeight - margin) {\n                    pdf.addPage();\n                    y = 50;\n                    return true;\n                }\n                return false;\n            };\n            const lines = content.split('\\n');\n            lines.forEach((line)=>{\n                pdf.setFont('helvetica', 'normal');\n                if (line.startsWith('# ')) {\n                    checkNewPage(40);\n                    pdf.setFontSize(20);\n                    pdf.setFont('helvetica', 'bold');\n                    const text = line.replace('# ', '');\n                    const wrappedText = splitTextToSize(text, 20);\n                    y += 20;\n                    wrappedText.forEach((textLine)=>{\n                        pdf.text(textLine, margin, y);\n                        y += 25;\n                    });\n                } else if (line.startsWith('## ')) {\n                    checkNewPage(35);\n                    pdf.setFontSize(16);\n                    pdf.setFont('helvetica', 'bold');\n                    const text = line.replace('## ', '');\n                    const wrappedText = splitTextToSize(text, 16);\n                    y += 15;\n                    wrappedText.forEach((textLine)=>{\n                        pdf.text(textLine, margin, y);\n                        y += 20;\n                    });\n                } else if (line.startsWith('### ')) {\n                    checkNewPage(30);\n                    pdf.setFontSize(14);\n                    pdf.setFont('helvetica', 'bold');\n                    const text = line.replace('### ', '');\n                    const wrappedText = splitTextToSize(text, 14);\n                    y += 15;\n                    wrappedText.forEach((textLine)=>{\n                        pdf.text(textLine, margin, y);\n                        y += 15;\n                    });\n                } else if (line.startsWith('- ')) {\n                    checkNewPage(25);\n                    pdf.setFontSize(11);\n                    const text = line.replace('- ', '');\n                    const wrappedText = splitTextToSize(text, 11);\n                    y += 15;\n                    pdf.circle(margin + 3, y - 4, 1.5, 'F');\n                    pdf.text(wrappedText, margin + 10, y);\n                    y += (wrappedText.length - 1) * 15;\n                } else if (line.trim().length > 0) {\n                    checkNewPage(25);\n                    pdf.setFontSize(11);\n                    const wrappedText = splitTextToSize(line, 11);\n                    y += 15;\n                    pdf.text(wrappedText, margin, y);\n                    y += (wrappedText.length - 1) * 15;\n                } else {\n                    y += 10;\n                }\n            });\n            pdf.save('smartsurf-research.pdf');\n        } catch (error) {\n            console.error('Failed to generate PDF:', error);\n        }\n    };\n    const handleGoogleDocs = ()=>{\n        const encodedContent = encodeURIComponent(content);\n        window.open(`https://docs.google.com/document/create?body=${encodedContent}`, '_blank');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleCopy,\n                className: \"px-3 py-1.5 text-xs rounded-lg bg-zinc-800/50 hover:bg-zinc-700/50  border border-zinc-700/50 text-zinc-300 transition-all duration-200 flex items-center gap-1.5\",\n                children: [\n                    copying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CheckIcon_DocumentIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-3.5 h-3.5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseActions.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 20\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CheckIcon_DocumentIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-3.5 h-3.5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseActions.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 60\n                    }, this),\n                    copying ? 'Copied!' : 'Copy'\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseActions.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            isResearchResponse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleTypeInDocs,\n                disabled: typing,\n                className: \"px-3 py-1.5 text-xs rounded-lg bg-zinc-800/50 hover:bg-zinc-700/50  border border-zinc-700/50 text-zinc-300 transition-all duration-200 flex items-center gap-1.5 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CheckIcon_DocumentIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-3.5 h-3.5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseActions.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this),\n                    typing ? 'Typing...' : 'Type in Docs'\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseActions.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleDownloadPDF,\n                className: \"px-3 py-1.5 text-xs rounded-lg bg-zinc-800/50 hover:bg-zinc-700/50  border border-zinc-700/50 text-zinc-300 transition-all duration-200 flex items-center gap-1.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CheckIcon_DocumentIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-3.5 h-3.5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseActions.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    \"Download PDF\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseActions.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseActions.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/rover/ResponseActions.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/rover/ResponseDisplay.tsx":
/*!**************************************************!*\
  !*** ./src/components/rover/ResponseDisplay.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponseDisplay: () => (/* binding */ ResponseDisplay),\n/* harmony export */   markdownComponents: () => (/* binding */ markdownComponents)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-syntax-highlighter */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_cjs_styles_prism__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-syntax-highlighter/dist/cjs/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/cjs/styles/prism/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ResponseActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ResponseActions */ \"(ssr)/./src/components/rover/ResponseActions.tsx\");\n\n\n\n\n\n\n\n\nfunction useTemporaryMessages(messages) {\n    const [visibleMessages, setVisibleMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useTemporaryMessages.useEffect\": ()=>{\n            const finalMessage = messages.find({\n                \"useTemporaryMessages.useEffect.finalMessage\": (m)=>m.type === 'final_answer' || m.type === 'final_response'\n            }[\"useTemporaryMessages.useEffect.finalMessage\"]);\n            if (finalMessage) {\n                setVisibleMessages([]);\n                return;\n            }\n            const streamingMessages = messages.filter({\n                \"useTemporaryMessages.useEffect.streamingMessages\": (m)=>m.type !== 'final_answer' && m.type !== 'final_response' && m.type !== 'user_input'\n            }[\"useTemporaryMessages.useEffect.streamingMessages\"]);\n            setVisibleMessages(streamingMessages);\n        }\n    }[\"useTemporaryMessages.useEffect\"], [\n        messages\n    ]);\n    return visibleMessages;\n}\n// Helper function to stringify message content\nconst formatMessageContent = (content)=>{\n    if (Array.isArray(content)) {\n        return content.join('\\n');\n    }\n    if (typeof content === 'string') {\n        try {\n            // Try to parse as JSON in case it's a stringified array\n            const parsed = JSON.parse(content);\n            if (Array.isArray(parsed)) {\n                return parsed.join('\\n');\n            }\n        } catch  {\n            // If parsing fails, return as is\n            return content;\n        }\n    }\n    return String(content);\n};\nconst markdownComponents = {\n    h1: ({ children, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n            className: \"text-3xl font-bold mt-8 mb-6 bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400  text-transparent bg-clip-text\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 82,\n            columnNumber: 5\n        }, undefined),\n    h2: ({ children, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n            className: \"text-2xl font-semibold mt-6 mb-4 text-indigo-300  border-b border-indigo-500/20 pb-2\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 88,\n            columnNumber: 5\n        }, undefined),\n    h3: ({ children, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n            className: \"text-xl font-medium mt-4 mb-3 text-purple-300\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 94,\n            columnNumber: 5\n        }, undefined),\n    p: ({ children, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-zinc-100 leading-7 mb-4\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 99,\n            columnNumber: 5\n        }, undefined),\n    ul: ({ children, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"my-4 space-y-2 list-none\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 104,\n            columnNumber: 5\n        }, undefined),\n    ol: ({ children, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n            className: \"my-4 space-y-2 list-decimal pl-4\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 109,\n            columnNumber: 5\n        }, undefined),\n    li: ({ children, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n            className: \"flex items-start\",\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-indigo-400 mr-2 font-bold\",\n                    children: \"•\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-zinc-100\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 114,\n            columnNumber: 5\n        }, undefined),\n    blockquote: ({ children, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n            className: \"border-l-4 border-indigo-500/50 bg-indigo-500/5  pl-6 py-4 my-6 rounded-r-lg italic text-zinc-300\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 120,\n            columnNumber: 5\n        }, undefined),\n    code: ({ inline, className, children, ...props })=>{\n        const match = /language-(\\w+)/.exec(className || '');\n        return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            style: react_syntax_highlighter_dist_cjs_styles_prism__WEBPACK_IMPORTED_MODULE_4__.oneDark,\n            language: match[1],\n            PreTag: \"div\",\n            className: \"!my-8 rounded-xl border border-white/10 !bg-zinc-900/50 !p-6\",\n            ...props,\n            children: String(children).replace(/\\n$/, '')\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n            className: \"bg-zinc-800/50 px-2 py-1 rounded-md font-mono text-sm text-indigo-300\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, undefined);\n    },\n    table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto my-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"w-full border-collapse\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 144,\n            columnNumber: 5\n        }, undefined),\n    th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n            className: \"text-left py-2 px-4 border-b border-zinc-800 text-indigo-300 font-semibold\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 151,\n            columnNumber: 5\n        }, undefined),\n    td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n            className: \"py-2 px-4 border-b border-zinc-800/50 text-zinc-100\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 156,\n            columnNumber: 5\n        }, undefined),\n    a: ({ children, href, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: href,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"text-indigo-400 hover:text-purple-400 underline decoration-indigo-500/30  hover:decoration-purple-500/50 decoration-2 underline-offset-2  transition-all duration-200 font-medium hover:scale-[1.02] inline-flex items-center gap-0.5\",\n            ...props,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-3.5 h-3.5 ml-1 -mt-0.5 opacity-70\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n            lineNumber: 161,\n            columnNumber: 5\n        }, undefined)\n};\nfunction ResponseDisplay({ messages }) {\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (messagesEndRef.current) {\n            const container = messagesEndRef.current.parentElement?.parentElement?.parentElement;\n            container?.scrollTo({\n                top: container.scrollHeight,\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Scroll on new messages with a slight delay to ensure content is rendered\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResponseDisplay.useEffect\": ()=>{\n            const timeoutId = setTimeout(scrollToBottom, 100);\n            return ({\n                \"ResponseDisplay.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"ResponseDisplay.useEffect\"];\n        }\n    }[\"ResponseDisplay.useEffect\"], [\n        messages\n    ]);\n    // Group messages by conversation turns\n    const messageGroups = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ResponseDisplay.useMemo[messageGroups]\": ()=>{\n            const groups = [];\n            let currentGroup = [];\n            messages.forEach({\n                \"ResponseDisplay.useMemo[messageGroups]\": (message)=>{\n                    if (message.type === 'user_input') {\n                        if (currentGroup.length > 0) {\n                            groups.push(currentGroup);\n                        }\n                        currentGroup = [\n                            message\n                        ];\n                    } else {\n                        currentGroup.push(message);\n                    }\n                }\n            }[\"ResponseDisplay.useMemo[messageGroups]\"]);\n            if (currentGroup.length > 0) {\n                groups.push(currentGroup);\n            }\n            return groups;\n        }\n    }[\"ResponseDisplay.useMemo[messageGroups]\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-6xl mx-auto space-y-8 px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                mode: \"popLayout\",\n                children: messageGroups.map((group, groupIndex)=>{\n                    const userMessage = group.find((m)=>m.type === 'user_input');\n                    const finalMessage = group.find((m)=>m.type === 'final_answer' || m.type === 'final_response');\n                    const streamingMessages = !finalMessage ? group.filter((m)=>m.type !== 'user_input' && m.type !== 'final_answer' && m.type !== 'final_response') : [];\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        className: \"space-y-4\",\n                        children: [\n                            userMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[90%] md:max-w-[75%] break-words bg-gradient-to-r from-indigo-500/20  via-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-indigo-500/30  rounded-2xl rounded-tr-sm px-5 py-3 shadow-lg shadow-purple-500/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-white/90 whitespace-pre-wrap\",\n                                        children: formatMessageContent(userMessage.content)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 17\n                            }, this),\n                            streamingMessages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                className: \"space-y-2\",\n                                children: streamingMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        className: \"flex justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `max-w-[90%] md:max-w-[75%] break-words backdrop-blur-sm border \n                        px-4 py-2 rounded-xl rounded-tl-sm\n                        ${message.type.includes('action') ? 'bg-emerald-500/10 border-emerald-500/30 shadow-emerald-500/20' : 'bg-zinc-500/10 border-zinc-500/30 shadow-zinc-500/20'} \n                        shadow-lg`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-white/70 whitespace-pre-wrap\",\n                                                children: formatMessageContent(message.content)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, `stream-${groupIndex}-${index}`, false, {\n                                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 17\n                            }, this),\n                            finalMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                className: \"flex flex-col justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[95%] md:max-w-[85%] break-words bg-gradient-to-br from-indigo-500/20  via-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-indigo-500/30  rounded-2xl rounded-tl-sm px-6 py-4 shadow-xl shadow-indigo-500/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_7__.Markdown, {\n                                            remarkPlugins: [\n                                                remark_gfm__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                            ],\n                                            components: markdownComponents,\n                                            className: \"prose prose-invert max-w-none space-y-4 overflow-x-hidden\",\n                                            children: formatMessageContent(finalMessage.content)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResponseActions__WEBPACK_IMPORTED_MODULE_2__.ResponseActions, {\n                                            content: formatMessageContent(finalMessage.content),\n                                            isResearchResponse: finalMessage.type === 'final_response' || finalMessage.type === 'final_answer'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, `group-${groupIndex}`, true, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: messagesEndRef,\n                className: \"h-px\"\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\rover\\\\ResponseDisplay.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/rover/ResponseDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ParticlesBackground.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/ParticlesBackground.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesBackground: () => (/* binding */ ParticlesBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ParticlesBackground auto */ \n\nfunction ParticlesBackground() {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParticlesBackground.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = window.innerWidth;\n            canvas.height = window.innerHeight;\n            const particles = [];\n            for(let i = 0; i < 50; i++){\n                particles.push({\n                    x: Math.random() * canvas.width,\n                    y: Math.random() * canvas.height,\n                    size: Math.random() * 2,\n                    speedX: (Math.random() - 0.5) * 0.5,\n                    speedY: (Math.random() - 0.5) * 0.5,\n                    opacity: Math.random() * 0.5\n                });\n            }\n            function animate() {\n                ctx.clearRect(0, 0, canvas.width, canvas.height);\n                particles.forEach({\n                    \"ParticlesBackground.useEffect.animate\": (particle)=>{\n                        particle.x += particle.speedX;\n                        particle.y += particle.speedY;\n                        if (particle.x > canvas.width) particle.x = 0;\n                        if (particle.x < 0) particle.x = canvas.width;\n                        if (particle.y > canvas.height) particle.y = 0;\n                        if (particle.y < 0) particle.y = canvas.height;\n                        ctx.beginPath();\n                        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n                        ctx.fillStyle = `rgba(147, 197, 253, ${particle.opacity})`;\n                        ctx.fill();\n                    }\n                }[\"ParticlesBackground.useEffect.animate\"]);\n                requestAnimationFrame(animate);\n            }\n            animate();\n            const handleResize = {\n                \"ParticlesBackground.useEffect.handleResize\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"ParticlesBackground.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"ParticlesBackground.useEffect\": ()=>window.removeEventListener('resize', handleResize)\n            })[\"ParticlesBackground.useEffect\"];\n        }\n    }[\"ParticlesBackground.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        className: \"fixed inset-0 pointer-events-none z-0\"\n    }, void 0, false, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ParticlesBackground.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ParticlesBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ToggleSwitch.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ToggleSwitch.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToggleSwitch: () => (/* binding */ ToggleSwitch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Switch_headlessui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Switch!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/switch/switch.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction ToggleSwitch({ enabled, onChange, label, description, hideDescription = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Switch_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Switch.Group, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Switch_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Switch, {\n                    checked: enabled,\n                    onChange: onChange,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(enabled ? 'bg-indigo-500' : 'bg-zinc-700', 'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-black'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(enabled ? 'translate-x-6' : 'translate-x-1', 'inline-block h-4 w-4 transform rounded-full bg-white transition-transform')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ToggleSwitch.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ToggleSwitch.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Switch_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Switch.Label, {\n                            className: \"text-sm font-medium text-zinc-200\",\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ToggleSwitch.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 21\n                        }, this),\n                        description && !hideDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Switch_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Switch.Description, {\n                            className: \"text-xs text-zinc-400\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ToggleSwitch.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ToggleSwitch.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ToggleSwitch.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ToggleSwitch.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ToggleSwitch.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUNKO0FBRWxDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXFdlYlJvdmVyLW1haW5cXGZyb250ZW5kXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSAnY2xzeCc7XG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSAndGFpbHdpbmQtbWVyZ2UnO1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn0iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"01927231985e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcV2ViUm92ZXItbWFpblxcZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjAxOTI3MjMxOTg1ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var geist_font__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! geist/font */ \"(rsc)/./node_modules/geist/dist/font.js\");\n/* harmony import */ var _components_ui_ParticlesBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ParticlesBackground */ \"(rsc)/./src/components/ui/ParticlesBackground.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"SmartSurf - AI Web Navigation Assistant\",\n    description: \"Your AI co-pilot for web navigation and task automation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${geist_font__WEBPACK_IMPORTED_MODULE_1__.GeistSans.variable} ${geist_font__WEBPACK_IMPORTED_MODULE_1__.GeistMono.variable}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"bg-gradient-to-b from-zinc-900 to-black text-white antialiased\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParticlesBackground__WEBPACK_IMPORTED_MODULE_2__.ParticlesBackground, {}, void 0, false, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDd0I7QUFDbkQ7QUFFaEIsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFXLEdBQUdWLGlEQUFTQSxDQUFDVyxRQUFRLENBQUMsQ0FBQyxFQUFFVixpREFBU0EsQ0FBQ1UsUUFBUSxFQUFFO2tCQUN0RSw0RUFBQ0M7WUFBS0YsV0FBVTs7OEJBQ2QsOERBQUNSLG1GQUFtQkE7Ozs7O2dCQUNuQks7Ozs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJEOlxcV2ViUm92ZXItbWFpblxcZnJvbnRlbmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgR2Vpc3RTYW5zLCBHZWlzdE1vbm8gfSBmcm9tIFwiZ2Vpc3QvZm9udFwiO1xuaW1wb3J0IHsgUGFydGljbGVzQmFja2dyb3VuZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvUGFydGljbGVzQmFja2dyb3VuZFwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJTbWFydFN1cmYgLSBBSSBXZWIgTmF2aWdhdGlvbiBBc3Npc3RhbnRcIixcbiAgZGVzY3JpcHRpb246IFwiWW91ciBBSSBjby1waWxvdCBmb3Igd2ViIG5hdmlnYXRpb24gYW5kIHRhc2sgYXV0b21hdGlvblwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9e2Ake0dlaXN0U2Fucy52YXJpYWJsZX0gJHtHZWlzdE1vbm8udmFyaWFibGV9YH0+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iIGZyb20temluYy05MDAgdG8tYmxhY2sgdGV4dC13aGl0ZSBhbnRpYWxpYXNlZFwiPlxuICAgICAgICA8UGFydGljbGVzQmFja2dyb3VuZCAvPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufSJdLCJuYW1lcyI6WyJHZWlzdFNhbnMiLCJHZWlzdE1vbm8iLCJQYXJ0aWNsZXNCYWNrZ3JvdW5kIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsImJvZHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/rover/page.tsx":
/*!********************************!*\
  !*** ./src/app/rover/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\rover\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\WebRover-main\\frontend\\src\\app\\rover\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/ParticlesBackground.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/ParticlesBackground.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ParticlesBackground: () => (/* binding */ ParticlesBackground)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ParticlesBackground = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ParticlesBackground() from the server but ParticlesBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\WebRover-main\\frontend\\src\\components\\ui\\ParticlesBackground.tsx",
"ParticlesBackground",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxXZWJSb3Zlci1tYWluXFxmcm9udGVuZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@babel","vendor-chunks/geist","vendor-chunks/@swc","vendor-chunks/refractor","vendor-chunks/framer-motion","vendor-chunks/react-syntax-highlighter","vendor-chunks/motion-dom","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/@headlessui","vendor-chunks/micromark-core-commonmark","vendor-chunks/hastscript","vendor-chunks/property-information","vendor-chunks/@react-aria","vendor-chunks/micromark","vendor-chunks/motion-utils","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/@heroicons","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/style-to-object","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/tailwind-merge","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-gfm","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/fflate","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/clsx","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/xtend","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/jspdf","vendor-chunks/inline-style-parser","vendor-chunks/hast-util-parse-selector","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frover%2Fpage&page=%2Frover%2Fpage&appPaths=%2Frover%2Fpage&pagePath=private-next-app-dir%2Frover%2Fpage.tsx&appDir=D%3A%5CWebRover-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebRover-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
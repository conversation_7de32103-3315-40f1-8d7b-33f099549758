/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWebRover-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebRover-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWebRover-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebRover-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWebRover-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebRover-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDV2ViUm92ZXItbWFpbiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1dlYlJvdmVyLW1haW4lNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2xpYiU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUE4SDtBQUM5SDtBQUNBLDBPQUFpSTtBQUNqSTtBQUNBLDBPQUFpSTtBQUNqSTtBQUNBLG9SQUF1SjtBQUN2SjtBQUNBLHdPQUFnSTtBQUNoSTtBQUNBLHNRQUErSTtBQUMvSTtBQUNBLHNPQUErSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJSb3Zlci1tYWluXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFdlYlJvdmVyLW1haW5cXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJSb3Zlci1tYWluXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV2ViUm92ZXItbWFpblxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFdlYlJvdmVyLW1haW5cXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxsaWJcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ParticlesBackground.tsx */ \"(rsc)/./src/components/ui/ParticlesBackground.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ParticlesBackground.tsx */ \"(ssr)/./src/components/ui/ParticlesBackground.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Inter%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Symbol%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cfont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Thin.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraLight.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Light.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Regular.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Medium.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-SemiBold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Black.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-UltraBlack.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CParticlesBackground.tsx%22%2C%22ids%22%3A%5B%22ParticlesBackground%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW9GIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJSb3Zlci1tYWluXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXZWJSb3Zlci1tYWluJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW9GIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXZWJSb3Zlci1tYWluXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebRover-main%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_SpotlightCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/SpotlightCard */ \"(ssr)/./src/components/ui/SpotlightCard.tsx\");\n/* harmony import */ var _components_ui_ParticlesBackground__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ParticlesBackground */ \"(ssr)/./src/components/ui/ParticlesBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const handleConnect = async ()=>{\n        setIsConnecting(true);\n        setError(null);\n        try {\n            console.log('Attempting to connect...');\n            const response = await fetch('http://localhost:8000/setup-browser', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    url: 'https://www.google.com'\n                })\n            });\n            console.log('Response status:', response.status);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || 'Failed to setup browser');\n            }\n            console.log('Connection successful, redirecting...');\n            await router.push('/rover');\n        } catch (error) {\n            console.error('Failed to connect:', error);\n            setError(error instanceof Error ? error.message : 'Failed to connect to browser');\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative min-h-screen flex items-center justify-center p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParticlesBackground__WEBPACK_IMPORTED_MODULE_4__.ParticlesBackground, {}, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SpotlightCard__WEBPACK_IMPORTED_MODULE_3__.SpotlightCard, {\n                className: \"w-full max-w-3xl mx-auto p-8 md:p-12\",\n                spotlightColor: \"rgba(139, 92, 246, 0.15)\",\n                gradient: \"from-indigo-500/20 via-purple-500/20 to-pink-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400  text-transparent bg-clip-text animate-flow bg-[length:200%_auto]\",\n                                    children: \"SmartSurf\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl text-zinc-400\",\n                                    children: \"Your AI Co-pilot for Web Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl\",\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-zinc-200\",\n                                            children: \"Smart Search\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-zinc-400\",\n                                            children: \"Intelligent web navigation and information retrieval\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl\",\n                                            children: \"\\uD83E\\uDD16\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-zinc-200\",\n                                            children: \"AI Powered\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-zinc-400\",\n                                            children: \"Advanced language models guide the navigation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl\",\n                                            children: \"⚡\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-zinc-200\",\n                                            children: \"Real-time\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-zinc-400\",\n                                            children: \"Live browser interaction and instant responses\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleConnect,\n                                    disabled: isConnecting,\n                                    className: \"w-full px-8 py-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full  text-white font-medium transition-all duration-300 hover:opacity-90 hover:shadow-lg hover:shadow-purple-500/25  disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-purple-500/50\",\n                                    children: isConnecting ? 'Connecting...' : 'Connect to Browser'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-red-400 text-center\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ParticlesBackground.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/ParticlesBackground.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesBackground: () => (/* binding */ ParticlesBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ParticlesBackground auto */ \n\nfunction ParticlesBackground() {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParticlesBackground.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            canvas.width = window.innerWidth;\n            canvas.height = window.innerHeight;\n            const particles = [];\n            for(let i = 0; i < 50; i++){\n                particles.push({\n                    x: Math.random() * canvas.width,\n                    y: Math.random() * canvas.height,\n                    size: Math.random() * 2,\n                    speedX: (Math.random() - 0.5) * 0.5,\n                    speedY: (Math.random() - 0.5) * 0.5,\n                    opacity: Math.random() * 0.5\n                });\n            }\n            function animate() {\n                ctx.clearRect(0, 0, canvas.width, canvas.height);\n                particles.forEach({\n                    \"ParticlesBackground.useEffect.animate\": (particle)=>{\n                        particle.x += particle.speedX;\n                        particle.y += particle.speedY;\n                        if (particle.x > canvas.width) particle.x = 0;\n                        if (particle.x < 0) particle.x = canvas.width;\n                        if (particle.y > canvas.height) particle.y = 0;\n                        if (particle.y < 0) particle.y = canvas.height;\n                        ctx.beginPath();\n                        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n                        ctx.fillStyle = `rgba(147, 197, 253, ${particle.opacity})`;\n                        ctx.fill();\n                    }\n                }[\"ParticlesBackground.useEffect.animate\"]);\n                requestAnimationFrame(animate);\n            }\n            animate();\n            const handleResize = {\n                \"ParticlesBackground.useEffect.handleResize\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"ParticlesBackground.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"ParticlesBackground.useEffect\": ()=>window.removeEventListener('resize', handleResize)\n            })[\"ParticlesBackground.useEffect\"];\n        }\n    }[\"ParticlesBackground.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        className: \"fixed inset-0 pointer-events-none z-0\"\n    }, void 0, false, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\ParticlesBackground.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ParticlesBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/SpotlightCard.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/SpotlightCard.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpotlightCard: () => (/* binding */ SpotlightCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction SpotlightCard({ children, className = \"\", spotlightColor = \"rgba(59, 130, 246, 0.15)\", gradient = \"from-blue-500/20 to-teal-500/20\" }) {\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [opacity, setOpacity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleMouseMove = (e)=>{\n        if (!divRef.current) return;\n        const rect = divRef.current.getBoundingClientRect();\n        setPosition({\n            x: e.clientX - rect.left,\n            y: e.clientY - rect.top\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: divRef,\n        onMouseMove: handleMouseMove,\n        onMouseEnter: ()=>setOpacity(1),\n        onMouseLeave: ()=>setOpacity(0),\n        className: `relative rounded-2xl border border-zinc-800 bg-zinc-900/50 \n                 backdrop-blur-sm overflow-hidden transition-all duration-300\n                 hover:border-zinc-700 hover:bg-zinc-900/80 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 bg-gradient-to-r ${gradient} opacity-100 \n                      transition-opacity duration-500`\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\SpotlightCard.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pointer-events-none absolute inset-0 transition-opacity duration-300\",\n                style: {\n                    opacity,\n                    background: `radial-gradient(600px circle at ${position.x}px ${position.y}px, ${spotlightColor}, transparent 40%)`\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\SpotlightCard.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\SpotlightCard.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\components\\\\ui\\\\SpotlightCard.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/SpotlightCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"01927231985e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcV2ViUm92ZXItbWFpblxcZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjAxOTI3MjMxOTg1ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var geist_font__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! geist/font */ \"(rsc)/./node_modules/geist/dist/font.js\");\n/* harmony import */ var _components_ui_ParticlesBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ParticlesBackground */ \"(rsc)/./src/components/ui/ParticlesBackground.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"SmartSurf - AI Web Navigation Assistant\",\n    description: \"Your AI co-pilot for web navigation and task automation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${geist_font__WEBPACK_IMPORTED_MODULE_1__.GeistSans.variable} ${geist_font__WEBPACK_IMPORTED_MODULE_1__.GeistMono.variable}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"bg-gradient-to-b from-zinc-900 to-black text-white antialiased\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParticlesBackground__WEBPACK_IMPORTED_MODULE_2__.ParticlesBackground, {}, void 0, false, {\n                    fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDd0I7QUFDbkQ7QUFFaEIsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFXLEdBQUdWLGlEQUFTQSxDQUFDVyxRQUFRLENBQUMsQ0FBQyxFQUFFVixpREFBU0EsQ0FBQ1UsUUFBUSxFQUFFO2tCQUN0RSw0RUFBQ0M7WUFBS0YsV0FBVTs7OEJBQ2QsOERBQUNSLG1GQUFtQkE7Ozs7O2dCQUNuQks7Ozs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJEOlxcV2ViUm92ZXItbWFpblxcZnJvbnRlbmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgR2Vpc3RTYW5zLCBHZWlzdE1vbm8gfSBmcm9tIFwiZ2Vpc3QvZm9udFwiO1xuaW1wb3J0IHsgUGFydGljbGVzQmFja2dyb3VuZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvUGFydGljbGVzQmFja2dyb3VuZFwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJTbWFydFN1cmYgLSBBSSBXZWIgTmF2aWdhdGlvbiBBc3Npc3RhbnRcIixcbiAgZGVzY3JpcHRpb246IFwiWW91ciBBSSBjby1waWxvdCBmb3Igd2ViIG5hdmlnYXRpb24gYW5kIHRhc2sgYXV0b21hdGlvblwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9e2Ake0dlaXN0U2Fucy52YXJpYWJsZX0gJHtHZWlzdE1vbm8udmFyaWFibGV9YH0+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iIGZyb20temluYy05MDAgdG8tYmxhY2sgdGV4dC13aGl0ZSBhbnRpYWxpYXNlZFwiPlxuICAgICAgICA8UGFydGljbGVzQmFja2dyb3VuZCAvPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufSJdLCJuYW1lcyI6WyJHZWlzdFNhbnMiLCJHZWlzdE1vbm8iLCJQYXJ0aWNsZXNCYWNrZ3JvdW5kIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsImJvZHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\WebRover-main\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\WebRover-main\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/ParticlesBackground.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/ParticlesBackground.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ParticlesBackground: () => (/* binding */ ParticlesBackground)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ParticlesBackground = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ParticlesBackground() from the server but ParticlesBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\WebRover-main\\frontend\\src\\components\\ui\\ParticlesBackground.tsx",
"ParticlesBackground",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxXZWJSb3Zlci1tYWluXFxmcm9udGVuZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/geist","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWebRover-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebRover-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
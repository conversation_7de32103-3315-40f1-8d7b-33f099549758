"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-from-markdown";
exports.ids = ["vendor-chunks/mdast-util-from-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-from-markdown/dev/lib/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-from-markdown/dev/lib/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromMarkdown: () => (/* binding */ fromMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/micromark/dev/lib/postprocess.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/micromark/dev/lib/parse.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/micromark/dev/lib/preprocess.js\");\n/* harmony import */ var micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-util-decode-numeric-character-reference */ \"(ssr)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js\");\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-util-decode-string */ \"(ssr)/./node_modules/micromark-util-decode-string/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/decode-named-character-reference/index.js\");\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/unist-util-stringify-position/lib/index.js\");\n/**\n * @import {\n *   Break,\n *   Blockquote,\n *   Code,\n *   Definition,\n *   Emphasis,\n *   Heading,\n *   Html,\n *   Image,\n *   InlineCode,\n *   Link,\n *   ListItem,\n *   List,\n *   Nodes,\n *   Paragraph,\n *   PhrasingContent,\n *   ReferenceType,\n *   Root,\n *   Strong,\n *   Text,\n *   ThematicBreak\n * } from 'mdast'\n * @import {\n *   Encoding,\n *   Event,\n *   Token,\n *   Value\n * } from 'micromark-util-types'\n * @import {Point} from 'unist'\n * @import {\n *   CompileContext,\n *   CompileData,\n *   Config,\n *   Extension,\n *   Handle,\n *   OnEnterError,\n *   Options\n * } from './types.js'\n */\n\n\n\n\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Turn markdown into a syntax tree.\n *\n * @overload\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @overload\n * @param {Value} value\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @param {Value} value\n *   Markdown to parse.\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding for when `value` is `Buffer`.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Root}\n *   mdast tree.\n */\nfunction fromMarkdown(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding\n    encoding = undefined\n  }\n\n  return compiler(options)(\n    (0,micromark__WEBPACK_IMPORTED_MODULE_0__.postprocess)(\n      (0,micromark__WEBPACK_IMPORTED_MODULE_1__.parse)(options)\n        .document()\n        .write((0,micromark__WEBPACK_IMPORTED_MODULE_2__.preprocess)()(value, encoding, true))\n    )\n  )\n}\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      characterReference: onexitcharacterreference,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  }\n\n  configure(config, (options || {}).mdastExtensions || [])\n\n  /** @type {CompileData} */\n  const data = {}\n\n  return compile\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {type: 'root', children: []}\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      data\n    }\n    /** @type {Array<number>} */\n    const listStack = []\n    let index = -1\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered ||\n        events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          const tail = listStack.pop()\n          ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof tail === 'number', 'expected list ot be open')\n          index = prepareList(events, tail, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      const handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          Object.assign(\n            {sliceSerialize: events[index][2].sliceSerialize},\n            context\n          ),\n          events[index][1]\n        )\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1]\n      const handler = tail[1] || defaultOnError\n      handler.call(context, undefined, tail[0])\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length > 0 ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n      end: point(\n        events.length > 0\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    // Call transforms.\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1\n    let containerBalance = -1\n    let listSpread = false\n    /** @type {Token | undefined} */\n    let listItem\n    /** @type {number | undefined} */\n    let lineIndex\n    /** @type {number | undefined} */\n    let firstBlankLineIndex\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index <= length) {\n      const event = events[index]\n\n      switch (event[1].type) {\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuote: {\n          if (event[0] === 'enter') {\n            containerBalance++\n          } else {\n            containerBalance--\n          }\n\n          atMarker = undefined\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank: {\n          if (event[0] === 'enter') {\n            if (\n              listItem &&\n              !atMarker &&\n              !containerBalance &&\n              !firstBlankLineIndex\n            ) {\n              firstBlankLineIndex = index\n            }\n\n            atMarker = undefined\n          }\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemValue:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemMarker:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefixWhitespace: {\n          // Empty.\n\n          break\n        }\n\n        default: {\n          atMarker = undefined\n        }\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix) ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered ||\n            event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered))\n      ) {\n        if (listItem) {\n          let tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank\n                listSpread = true\n              }\n\n              tailEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuotePrefix ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuotePrefixWhitespace ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuoteMarker ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemIndent\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = Object.assign(\n            {},\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix) {\n          /** @type {Token} */\n          const item = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          }\n          listItem = item\n          events.splice(index, 0, ['enter', item, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Nodes} create\n   *   Create a node.\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['buffer']}\n   */\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  /**\n   * @type {CompileContext['enter']}\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(parent, 'expected `parent`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('children' in parent, 'expected `parent`')\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children\n    siblings.push(node)\n    this.stack.push(node)\n    this.tokenStack.push([token, errorHandler || undefined])\n    node.position = {\n      start: point(token.start),\n      // @ts-expect-error: `end` will be patched later.\n      end: undefined\n    }\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['exit']}\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected `node`')\n    const open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0])\n      } else {\n        const handler = open[1] || defaultOnError\n        handler.call(this, token, open[0])\n      }\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type !== 'fragment', 'unexpected fragment `exit`ed')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.position, 'expected `position` to be defined')\n    node.position.end = point(token.end)\n  }\n\n  /**\n   * @type {CompileContext['resume']}\n   */\n  function resume() {\n    return (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__.toString)(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    this.data.expectingFirstListItemValue = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (this.data.expectingFirstListItemValue) {\n      const ancestor = this.stack[this.stack.length - 2]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor, 'expected nodes on stack')\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor.type === 'list', 'expected list on stack')\n      ancestor.start = Number.parseInt(\n        this.sliceSerialize(token),\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal\n      )\n      this.data.expectingFirstListItemValue = undefined\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'code', 'expected code on stack')\n    node.lang = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'code', 'expected code on stack')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (this.data.flowCodeInside) return\n    this.buffer()\n    this.data.flowCodeInside = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    this.data.flowCodeInside = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.label = label\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'heading', 'expected heading on stack')\n\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length\n\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n        depth === 1 ||\n          depth === 2 ||\n          depth === 3 ||\n          depth === 4 ||\n          depth === 5 ||\n          depth === 6,\n        'expected `depth` between `1` and `6`'\n      )\n\n      node.depth = depth\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    this.data.setextHeadingSlurpLineEnding = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'heading', 'expected heading on stack')\n\n    node.depth =\n      this.sliceSerialize(token).codePointAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__.codes.equalsTo ? 1 : 2\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    this.data.setextHeadingSlurpLineEnding = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('children' in node, 'expected parent on stack')\n    /** @type {Array<Nodes>} */\n    const siblings = node.children\n\n    let tail = siblings[siblings.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      tail.position = {\n        start: point(token.start),\n        // @ts-expect-error: we’ll add `end` later.\n        end: undefined\n      }\n      siblings.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, 'expected a `node` to be on the stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('value' in tail, 'expected a `literal` to be on the stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, 'expected `node` to have an open position')\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(context, 'expected `node`')\n\n    // If we’re at a hard break, include the line ending in there.\n    if (this.data.atHardBreak) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('children' in context, 'expected `parent`')\n      const tail = context.children[context.children.length - 1]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, 'expected tail to have a starting position')\n      tail.position.end = point(token.end)\n      this.data.atHardBreak = undefined\n      return\n    }\n\n    if (\n      !this.data.setextHeadingSlurpLineEnding &&\n      config.canContainEols.includes(context.type)\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    this.data.atHardBreak = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'inlineCode', 'expected inline code on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'link', 'expected link on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'image', 'expected image on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token)\n    const ancestor = this.stack[this.stack.length - 2]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor, 'expected ancestor on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      ancestor.type === 'image' || ancestor.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__.decodeString)(string)\n    // @ts-expect-error: same as above.\n    ancestor.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(string).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(fragment, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(fragment.type === 'fragment', 'expected fragment on stack')\n    const value = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // Assume a reference.\n    this.data.inReference = true\n\n    if (node.type === 'link') {\n      /** @type {Array<PhrasingContent>} */\n      const children = fragment.children\n\n      node.children = children\n    } else {\n      node.alt = value\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    this.data.inReference = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    this.data.referenceType = 'collapsed'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image reference or link reference on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label\n    // @ts-expect-error: same as above.\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    this.data.referenceType = 'full'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      token.type === 'characterReferenceMarkerNumeric' ||\n        token.type === 'characterReferenceMarkerHexadecimal'\n    )\n    this.data.characterReferenceType = token.type\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token)\n    const type = this.data.characterReferenceType\n    /** @type {string} */\n    let value\n\n    if (type) {\n      value = (0,micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__.decodeNumericCharacterReference)(\n        data,\n        type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterReferenceMarkerNumeric\n          ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal\n          : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseHexadecimal\n      )\n      this.data.characterReferenceType = undefined\n    } else {\n      const result = (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__.decodeNamedCharacterReference)(data)\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result !== false, 'expected reference to decode')\n      value = result\n    }\n\n    const tail = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, 'expected `node`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('value' in tail, 'expected `node.value`')\n    tail.value += value\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreference(token) {\n    const tail = this.stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, 'expected `node`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, 'expected `node.position`')\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'link', 'expected link on stack')\n\n    node.url = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'link', 'expected link on stack')\n\n    node.url = 'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    return {\n      type: 'heading',\n      // @ts-expect-error `depth` will be set later.\n      depth: 0,\n      children: []\n    }\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  /** @returns {Html} */\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {line: d.line, column: d.column, offset: d.offset}\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Array<Extension> | Extension>} extensions\n * @returns {undefined}\n */\nfunction configure(combined, extensions) {\n  let index = -1\n\n  while (++index < extensions.length) {\n    const value = extensions[index]\n\n    if (Array.isArray(value)) {\n      configure(combined, value)\n    } else {\n      extension(combined, value)\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {undefined}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'canContainEols': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'transforms': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'enter':\n        case 'exit': {\n          const right = extension[key]\n          if (right) {\n            Object.assign(combined[key], right)\n          }\n\n          break\n        }\n        // No default\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error(\n      'Cannot close `' +\n        left.type +\n        '` (' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: left.start, end: left.end}) +\n        '): a different token (`' +\n        right.type +\n        '`, ' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: right.start, end: right.end}) +\n        ') is open'\n    )\n  } else {\n    throw new Error(\n      'Cannot close document, a token (`' +\n        right.type +\n        '`, ' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: right.start, end: right.end}) +\n        ') is still open'\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-from-markdown/dev/lib/index.js\n");

/***/ })

};
;